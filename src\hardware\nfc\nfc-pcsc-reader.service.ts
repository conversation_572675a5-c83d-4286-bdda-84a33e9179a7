import { Injectable, Logger } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { NfcReaderService } from '../interfaces/nfc-reader.interface';
import { NFC } from 'nfc-pcsc';

@Injectable()
export class PcscLiteReaderService implements NfcReaderService {
  private readonly tag$ = new Subject<string>();
  private readonly log = new Logger(PcscLiteReaderService.name);

  constructor() {
    this.log.log('📱 Initializing PCSC NFC reader...');

    // Initialize PCSC asynchronously to prevent blocking
    setTimeout(() => {
      try {
        const pcsc = new NFC();

        pcsc.on('reader', reader => {
          this.log.log(`📱 NFC Reader: ${reader.name}`);

          reader.on('card', card => {
            this.log.log(`🏷️ Badge detected: ${card.uid}`);
            this.tag$.next(card.uid);
          });

          reader.on('error', err => {
            this.log.error(`NFC Error: ${err.message}`);
          });

          reader.on('end', () => {
            this.log.warn(`📱 Reader disconnected: ${reader.name}`);
          });
        });

        pcsc.on('error', err => {
          this.log.error(`PCSC Error: ${err.message}`);
        });

        this.log.log('✅ PCSC NFC reader ready');
      } catch (error) {
        this.log.error(`Failed to initialize PCSC: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }, 500);
  }

  onTag(): Observable<string> {
    this.log.log(`📤 onTag() called - returning observable`);
    return this.tag$.asObservable();
  }
}
