import { BrowserRouter as Router, useLocation } from 'react-router-dom';
import { WebSocketProvider } from './context/WebSocketContext';
import { AudioProvider } from './context/AudioContext';
import DebugPanel from './components/DebugPanel';
import Home from './pages/Home';
import Welcome from './pages/Welcome';
import Instructions from './pages/Instructions';
import Lost from './pages/Lost';
import Won from './pages/Won';
import Jackpot from './pages/Jackpot';

function AppContent() {
  const location = useLocation();
  const isDevelopment = import.meta.env.DEV;

  const getCurrentPage = () => {
    switch (location.pathname) {
      case '/':
        return <Home />;
      case '/welcome':
        return <Welcome />;
      case '/instructions':
        return <Instructions />;
      case '/lost':
        return <Lost />;
      case '/won':
        return <Won />;
      case '/jackpot':
        return <Jackpot />;
      default:
        return <Home />;
    }
  };

  return (
    <div className="w-full h-full overflow-hidden relative" style={{ width: '1920px', height: '1080px', maxWidth: '1920px', maxHeight: '1080px' }}>
      <AudioProvider>
        <WebSocketProvider>
          <div className="w-full h-full transition-opacity duration-300 ease-in-out opacity-100">
            {getCurrentPage()}
          </div>

          <DebugPanel isVisible={isDevelopment} />
        </WebSocketProvider>
      </AudioProvider>
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App
