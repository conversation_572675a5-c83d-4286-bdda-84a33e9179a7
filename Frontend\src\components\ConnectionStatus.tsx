import { useWebSocket } from '../context/WebSocketContext';

interface ConnectionStatusProps {
  showDetails?: boolean;
  className?: string;
}

export default function ConnectionStatus({ showDetails = false, className = '' }: ConnectionStatusProps) {
  const { gameState, isConnected } = useWebSocket();

  if (!showDetails) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        <span className="text-sm">
          {isConnected ? 'Connecté' : 'Déconnecté'}
        </span>
      </div>
    );
  }

  return (
    <div className={`p-4 bg-gray-100 rounded-lg ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
        <span className="font-semibold">
          {isConnected ? 'Connecté au serveur' : 'Déconnecté du serveur'}
        </span>
      </div>
      
      {gameState.connectionError && (
        <div className="text-red-600 text-sm mb-2">
          Erreur: {gameState.connectionError}
        </div>
      )}
      
      {gameState.reconnectAttempts > 0 && (
        <div className="text-yellow-600 text-sm mb-2">
          Tentatives de reconnexion: {gameState.reconnectAttempts}
        </div>
      )}
      
      {gameState.lastConnectedAt && (
        <div className="text-gray-600 text-xs">
          Dernière connexion: {gameState.lastConnectedAt.toLocaleTimeString()}
        </div>
      )}
      
      <div className="text-gray-600 text-xs mt-2">
        État du jeu: {gameState.gameStatus}
      </div>
    </div>
  );
}
