export interface Player {
  name: string;
  points: number;
  isWon: boolean;
  isJackpot: boolean;
}

export interface GameState {
  isConnected: boolean;
  connectionError: string | null;
  reconnectAttempts: number;
  lastConnectedAt: Date | null;

  gameName: string;
  teamName: string;
  instructions: string;

  gameTimer: number;
  sessionDuration: number | null;

  totalScore: number;
  gameScore: number;
  players: Player[];

  currentRoute: string;
  gameStatus: 'waiting' | 'welcome' | 'playing' | 'complete';

  jackpotThreshold: number;
  minThreshold: number;

  gameResult: 'won' | 'lost' | 'jackpot' | null;
  resultDescription: string;
}

export interface StartPayload {
  gameName: string;
  teamName: string;
  instructions: string;
  gameTimer: number;
  sessionDuration: number | null;
  teamScore: number;
  jackpotThreshold: number;
  minThreshold: number;
}

export interface GameCompletePayload {
  players: Player[];
  gameScore: number;
}

export interface BonusPayload {
  points: number;
}

export interface EndPayload {
  points: number;
}

export interface CellReadyPayload {
  message: string;
}

export interface TransitionToWinPayload {
  message: string;
}

export type WebSocketMessage =
  | { action: 'start'; payload: StartPayload }
  | { action: 'game_complete'; payload: GameCompletePayload }
  | { action: 'bonus'; payload: BonusPayload }
  | { action: 'end'; payload: EndPayload }
  | { action: 'cell_ready'; payload: CellReadyPayload }
  | { action: 'transition_to_win'; payload: TransitionToWinPayload }
  | { action: 'reset'; payload: any };

export interface WebSocketContextType {
  gameState: GameState;
  isConnected: boolean;
  connect: () => void;
  disconnect: () => void;
  sendMessage: (message: WebSocketMessage) => void;
}

export interface TimerHookReturn {
  gameTimer: string;
  sessionTimer: string;
  isGameTimerActive: boolean;
  isSessionTimerActive: boolean;
  startGameTimer: (seconds: number) => void;
  startSessionTimer: (seconds: number) => void;
  stopGameTimer: () => void;
  stopSessionTimer: () => void;
  resetTimers: () => void;
}

export type GameRoute =
  | '/'
  | '/welcome'
  | '/instructions'
  | '/lost'
  | '/won'
  | '/jackpot';

export interface NavigationState {
  currentRoute: GameRoute;
  previousRoute: GameRoute | null;
  autoNavigationEnabled: boolean;
}
