{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "noImplicitAny": false, "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["node", "jest"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*", "ecosystem.config.js"], "exclude": ["node_modules", "dist"]}