import Container from "../components/Container";
import ContentText from "../components/ContentText";
import { useGameState } from "../hooks/useGameState";

export default function Instructions() {
  const { gameState } = useGameState();

  return (
    <Container background='rideaux-rouge.gif'>
      <ContentText ticket='ticket-simple.gif' personnage='instructions.png' title='instructions' status="SIMPLE">
        <div className="text-[40px]" dangerouslySetInnerHTML={{ __html: gameState.instructions || "Visez la cible et obtenez un <br />maximum de points" }} />
      </ContentText>
    </Container>
  )
}
