{"port": 3000, "gameId": 4, "stationId": "RPI-01", "mode": "PROD", "api": {"baseUrl": "https://vmi1015553.contaboserver.net:9010/api/game-manager"}, "hardware": {"nfcReaderType": "PCSC", "modes": {"controllino": "SIM", "ledControl": "SIM"}, "rs232": {"baudRate": 9600, "vendorId": "1a86", "defaultPort": "/dev/ttyUSB0"}, "controllino": {"spiBus": 0, "deviceId": 1, "serial": {"baudRate": 9600, "vendorId": "2341", "defaultPort": "/dev/ttyACM0"}, "outputs": {"redLed": "01", "greenLed": "02", "arcadeMachine": "04", "door": "05", "cellLight": "99"}, "states": {"on": "1", "off": "0"}}}, "adminBadges": ["faae8f0c"], "game": {"thresholds": {"jackpot": 4000, "minWin": 500}, "timers": {"gameDefault": 300, "gameSimulation": 30, "gameTimeout": 5}, "displayTimings": {"exitBuffer": 10, "jackpotDisplay": 10, "winDisplay": 10, "resultDisplay": 10}}}