import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NfcReaderService } from '../interfaces/nfc-reader.interface';
import { PcscLiteReaderService } from './nfc-pcsc-reader.service';
import { Rs232ReaderService } from './rs232-reader.service';
import { Subject, Observable, merge } from 'rxjs';
import * as readline from 'node:readline';

@Injectable()
export class HybridNfcReaderService implements NfcReaderService {
  private readonly log = new Logger(HybridNfcReaderService.name);
  private readonly tag$ = new Subject<string>();
  private rl!: readline.Interface;
  private hardwareReader?: NfcReaderService;

  constructor(private cfg: ConfigService) {
    // Always setup mock NFC (press 'b' functionality)
    this.setupMockNfc();

    // Optionally setup hardware reader based on config
    this.setupHardwareReader();
  }

  onTag(): Observable<string> {
    if (this.hardwareReader) {
      // Merge both hardware and mock tag streams
      return merge(this.tag$.asObservable(), this.hardwareReader.onTag());
    } else {
      // Only mock tags
      return this.tag$.asObservable();
    }
  }

  private setupMockNfc(): void {
    console.log('🎮 NFC Simulation Controls:');
    console.log('  Press "b" + Enter to enter a badge ID');
    console.log('  Press Ctrl+C to exit');

    // Create a single persistent readline interface
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '',
    });

    // Handle line input (when user presses Enter)
    this.rl.on('line', (input) => {
      const trimmed = input.trim().toLowerCase();

      if (trimmed === 'b') {
        this.promptForBadgeId();
      } else if (trimmed.length > 0) {
        // Treat any other input as a badge ID
        console.log(`📱 Simulating badge scan: ${trimmed.toUpperCase()}`);
        this.tag$.next(trimmed.toUpperCase());
        console.log('\n🎮 Press "b" + Enter to enter another badge ID');
      }
    });

    // Handle Ctrl+C
    this.rl.on('SIGINT', () => {
      console.log('\n👋 Exiting...');
      process.exit();
    });

    console.log('\n🎮 Press "b" + Enter to enter a badge ID');
  }

  private setupHardwareReader(): void {
    const readerType = this.cfg.get<string>('global.hardware.nfcReaderType');
    if (readerType === undefined) {
      throw new Error('Missing required config: global.hardware.nfcReaderType');
    }

    switch (readerType) {
      case 'PCSC':
        this.log.log('Initializing PCSC hardware reader...');
        // Initialize PCSC asynchronously to avoid blocking
        setTimeout(() => {
          try {
            // Set a timeout for PCSC initialization
            const initTimeout = setTimeout(() => {
              this.log.warn('PCSC initialization timed out after 5 seconds');
            }, 5000);

            this.hardwareReader = new PcscLiteReaderService();
            clearTimeout(initTimeout);
            this.log.log('PCSC hardware reader initialized successfully');
          } catch (error) {
            this.log.warn(`Failed to initialize PCSC reader: ${error}`);
          }
        }, 100);
        break;

      case 'RS232':
        this.log.log('Initializing RS232 hardware reader...');
        // Initialize RS232 asynchronously to avoid blocking
        setTimeout(() => {
          try {
            this.hardwareReader = new Rs232ReaderService(this.cfg);
            this.log.log('RS232 hardware reader initialized successfully');
          } catch (error) {
            this.log.warn(`Failed to initialize RS232 reader: ${error}`);
          }
        }, 100);
        break;

      case 'NONE':
      default:
        this.log.log('No hardware NFC reader configured - using mock only');
        break;
    }
  }

  private promptForBadgeId(): void {
    this.rl.question('📱 Enter badge ID: ', (badgeId) => {
      if (badgeId.trim()) {
        console.log(
          `📱 Simulating badge scan: ${badgeId.trim().toUpperCase()}`,
        );
        this.tag$.next(badgeId.trim().toUpperCase());
      }
      console.log('\n🎮 Press "b" + Enter to enter another badge ID');
    });
  }
}
