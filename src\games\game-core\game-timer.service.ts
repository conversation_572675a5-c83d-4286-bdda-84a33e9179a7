import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiService } from '../../api/api.service';

export interface GameTimerConfig {
  gameTimer: number; // Game duration in seconds
  jackpotThreshold: number; // Jackpot threshold
  minThreshold: number; // Minimum threshold for winning
  gameName?: string; // Game name
  instructions?: string; // Game instructions
  source: 'API' | 'CONFIG' | 'DEFAULT'; // Source of the configuration
}

@Injectable()
export class GameTimerService {
  private readonly log = new Logger(GameTimerService.name);

  constructor(private cfg: ConfigService, private api: ApiService) {}

  /**
   * Get game timer configuration with API fallback
   */
  async getGameTimerConfig(gameId: number): Promise<GameTimerConfig> {
    // Always fetch fresh from API
    try {
      const apiConfig = await this.fetchFromApi(gameId);
      return apiConfig;
    } catch (error) {
      this.log.warn(
        `[GAME_TIMER] API fetch failed for game ${gameId}, using fallback`,
      );
      const fallbackConfig = this.getFallbackConfig();
      return fallbackConfig;
    }
  }

  /**
   * Get timeout for production mode (game timer + buffer)
   */
  async getProdTimeout(gameId: number): Promise<number> {
    const config = await this.getGameTimerConfig(gameId);
    const bufferSeconds = this.cfg.get<number>(
      'global.game.timers.gameTimeout',
    );
    if (bufferSeconds === undefined) {
      throw new Error(
        'Missing required config: global.game.timers.gameTimeout',
      );
    }
    const prodTimeout = (config.gameTimer + bufferSeconds) * 1000; // Convert to milliseconds

    this.log.log(
      `[GAME_TIMER] PROD timeout: ${
        config.gameTimer
      }s + ${bufferSeconds}s buffer = ${prodTimeout / 1000}s (${
        config.source
      })`,
    );
    return prodTimeout;
  }

  /**
   * Get timeout for simulation mode
   */
  async getSimTimeout(gameId: number): Promise<number> {
    const config = await this.getGameTimerConfig(gameId);
    const simTimeout = config.gameTimer * 1000; // Convert to milliseconds

    this.log.log(
      `[GAME_TIMER] SIM timeout: ${config.gameTimer}s (${config.source})`,
    );
    return simTimeout;
  }

  /**
   * Get safety backup timeout for simulation mode (sim timeout + buffer)
   */
  async getSimBackupTimeout(gameId: number): Promise<number> {
    const config = await this.getGameTimerConfig(gameId);
    const bufferSeconds = this.cfg.get<number>(
      'global.game.timers.gameTimeout',
    );
    if (bufferSeconds === undefined) {
      throw new Error(
        'Missing required config: global.game.timers.gameTimeout',
      );
    }
    const backupTimeout = (config.gameTimer + bufferSeconds) * 1000; // Convert to milliseconds

    this.log.log(
      `[GAME_TIMER] SIM backup timeout: ${
        config.gameTimer
      }s + ${bufferSeconds}s buffer = ${backupTimeout / 1000}s (${
        config.source
      })`,
    );
    return backupTimeout;
  }

  private async fetchFromApi(gameId: number): Promise<GameTimerConfig> {
    this.log.log(
      `[GAME_TIMER] Fetching game configuration from API for game ${gameId}`,
    );

    const apiResponse = await this.api.getGameConfig(gameId);

    // Log what we received from API
    this.log.log(
      `[GAME_TIMER] API Response - Name: ${
        apiResponse.name || 'null'
      }, Timer: ${apiResponse.gameTimer || 'null'}, Jackpot: ${
        apiResponse.jackpotThreshold || 'null'
      }, MinThreshold: ${apiResponse.minThreshold || 'null'}`,
    );

    // Use fallback values for null/invalid API responses
    const fallbackGameTimer = this.cfg.get<number>(
      'global.game.timers.gameDefault',
    );
    const fallbackJackpotThreshold = this.cfg.get<number>(
      'global.game.thresholds.jackpot',
    );
    const fallbackMinThreshold = this.cfg.get<number>(
      'global.game.thresholds.minWin',
    );

    if (fallbackGameTimer === undefined) {
      throw new Error(
        'Missing required config: global.game.timers.gameDefault',
      );
    }
    if (fallbackJackpotThreshold === undefined) {
      throw new Error(
        'Missing required config: global.game.thresholds.jackpot',
      );
    }
    if (fallbackMinThreshold === undefined) {
      throw new Error('Missing required config: global.game.thresholds.minWin');
    }

    const config: GameTimerConfig = {
      gameTimer:
        apiResponse.gameTimer && apiResponse.gameTimer > 0
          ? apiResponse.gameTimer
          : fallbackGameTimer,
      jackpotThreshold:
        apiResponse.jackpotThreshold && apiResponse.jackpotThreshold > 0
          ? apiResponse.jackpotThreshold
          : fallbackJackpotThreshold,
      minThreshold:
        apiResponse.minThreshold && apiResponse.minThreshold > 0
          ? apiResponse.minThreshold
          : fallbackMinThreshold,
      gameName: apiResponse.name || 'Unknown Game',
      instructions: apiResponse.instructions || undefined,
      source: 'API',
    };

    // Log what values we're actually using
    this.log.log(
      `[GAME_TIMER] Final API config - Timer: ${config.gameTimer}s${
        config.gameTimer !== apiResponse.gameTimer ? ' (fallback)' : ''
      }, Jackpot: ${config.jackpotThreshold}${
        config.jackpotThreshold !== apiResponse.jackpotThreshold
          ? ' (fallback)'
          : ''
      }, MinThreshold: ${config.minThreshold}${
        config.minThreshold !== apiResponse.minThreshold ? ' (fallback)' : ''
      }`,
    );

    return config;
  }

  private getFallbackConfig(): GameTimerConfig {
    const mode = this.cfg.get<string>('global.mode');
    if (mode === undefined) {
      throw new Error('Missing required config: global.mode');
    }

    const gameTimerConfig =
      mode === 'SIM'
        ? this.cfg.get<number>('global.game.timers.gameSimulation')
        : this.cfg.get<number>('global.game.timers.gameDefault');

    if (gameTimerConfig === undefined) {
      throw new Error(
        `Missing required config: global.game.timers.${
          mode === 'SIM' ? 'gameSimulation' : 'gameDefault'
        }`,
      );
    }

    const gameTimer = gameTimerConfig;

    const jackpotThreshold = this.cfg.get<number>(
      'global.game.thresholds.jackpot',
    );
    const minThreshold = this.cfg.get<number>('global.game.thresholds.minWin');

    if (jackpotThreshold === undefined) {
      throw new Error(
        'Missing required config: global.game.thresholds.jackpot',
      );
    }
    if (minThreshold === undefined) {
      throw new Error('Missing required config: global.game.thresholds.minWin');
    }

    const config: GameTimerConfig = {
      gameTimer,
      jackpotThreshold,
      minThreshold,
      gameName: 'Default Game',
      instructions: undefined,
      source: 'CONFIG',
    };

    this.log.log(
      `[GAME_TIMER] Using fallback config: ${config.gameTimer}s timer (${mode} mode), jackpot: ${config.jackpotThreshold}, minThreshold: ${config.minThreshold}`,
    );
    return config;
  }
}
