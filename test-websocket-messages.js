#!/usr/bin/env node

/**
 * Test script to simulate WebSocket messages for frontend testing
 */

const WebSocket = require('ws');

const WS_URL = 'ws://localhost:8000/ws';

console.log('🧪 WebSocket Message Test Script');
console.log('================================');
console.log(`📡 Connecting to: ${WS_URL}`);

const ws = new WebSocket(WS_URL);

ws.on('open', function open() {
  console.log('✅ Connected to WebSocket server');
  
  // Test sequence
  setTimeout(() => {
    console.log('📤 Sending start message...');
    ws.send(JSON.stringify({
      action: 'start',
      payload: {
        gameName: 'Mini Golf Challenge',
        teamName: 'Team Alpha',
        instructions: 'Visez la cible et obtenez un maximum de points',
        gameTimer: 240,        // 4 minutes
        sessionDuration: 1200, // 20 minutes
        jackpotThreshold: 5000,
        minThreshold: 1000
      }
    }));
  }, 1000);

  // Test bonus message
  setTimeout(() => {
    console.log('📤 Sending bonus message...');
    ws.send(JSON.stringify({
      action: 'bonus',
      payload: {
        points: 500
      }
    }));
  }, 8000);

  // Test game complete message
  setTimeout(() => {
    console.log('📤 Sending game complete message...');
    ws.send(JSON.stringify({
      action: 'game_complete',
      payload: {
        players: [
          { name: 'Player 1', points: 1500, isWon: true, isJackpot: false },
          { name: 'Player 2', points: 800, isWon: false, isJackpot: false },
          { name: 'Player 3', points: 300, isWon: false, isJackpot: false }
        ]
      }
    }));
  }, 15000);

  // Test cell ready message
  setTimeout(() => {
    console.log('📤 Sending cell ready message...');
    ws.send(JSON.stringify({
      action: 'cell_ready',
      payload: {
        message: 'Cell is ready for new team'
      }
    }));
  }, 25000);

  // Close connection
  setTimeout(() => {
    console.log('👋 Closing connection...');
    ws.close();
  }, 30000);
});

ws.on('message', function message(data) {
  console.log('📥 Received:', data.toString());
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket error:', err.message);
});

ws.on('close', function close() {
  console.log('🔌 Connection closed');
  process.exit(0);
});

// Handle Ctrl+C
process.on('SIGINT', function() {
  console.log('\n👋 Closing WebSocket connection...');
  ws.close();
  process.exit(0);
});
