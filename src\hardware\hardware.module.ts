import { DynamicModule, Module, Provider } from '@nestjs/common';
import { NFC_READER, SENSOR_BUS, DISPLAY, LED_CONTROL } from './tokens';
import { MockSensorBusService } from '../simulation/mocks/mock-sensor-bus.service';
import { MockLedService } from '../simulation/mocks/mock-led.service';
import { HybridNfcReaderService } from './nfc/hybrid-nfc-reader.service';
import { ControllinoSensorService } from './sensors/controllino-sensor.service';
import { ControllinoLedService } from './leds/controllino-led.service';
import { UiModule } from '../ui/ui.module';
import { WebsocketDisplayService } from 'src/ui/websocket-display.service';
import { ConfigService } from '@nestjs/config';

@Module({})
export class HardwareModule {
  static register(mode: 'SIM' | 'PROD'): DynamicModule {
    const providers: Provider[] = [
      {
        provide: NFC_READER,
        useFactory: (cfg: ConfigService) => {
          // Always use hybrid service (mock + optional hardware)
          return new HybridNfcReaderService(cfg);
        },
        inject: [ConfigService],
      },

      {
        provide: SENSOR_BUS,
        useFactory: (cfg: ConfigService) => {
          // Check individual hardware mode for Controllino
          const controllinoMode = cfg.get<string>('global.hardware.modes.controllino', mode);
          return controllinoMode === 'PROD'
            ? new ControllinoSensorService(cfg)
            : new MockSensorBusService(cfg);
        },
        inject: [ConfigService],
      },

      {
        provide: LED_CONTROL,
        useFactory: (cfg: ConfigService, sensorBus: any) => {
          // Check individual hardware mode for LED control
          const ledMode = cfg.get<string>('global.hardware.modes.ledControl', mode);
          return ledMode === 'PROD'
            ? new ControllinoLedService(cfg, sensorBus)
            : new MockLedService();
        },
        inject: [ConfigService, SENSOR_BUS],
      },

      { provide: DISPLAY, useClass: WebsocketDisplayService },
    ];

    return {
      module: HardwareModule,
      imports: [UiModule],
      providers,
      exports: providers,
      global: true, // Make providers globally available
    };
  }
}
