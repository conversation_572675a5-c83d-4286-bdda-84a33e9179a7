import { useWebSocket } from '../context/WebSocketContext';
import { useTimers } from './useTimers';
import type { Player } from '../types';

export function useGameState() {
  const { gameState, isConnected, sendMessage } = useWebSocket();
  const { gameTimer, sessionTimer, isGameTimerActive, isSessionTimerActive } =
    useTimers();

  const isGameActive =
    gameState.gameStatus === 'playing' || gameState.gameStatus === 'welcome';
  const isWaitingForGame = gameState.gameStatus === 'waiting';
  const isGameComplete = gameState.gameStatus === 'complete';

  const sortedPlayers = [...gameState.players].sort((a, b) => {
    if (b.points !== a.points) {
      return b.points - a.points;
    }
    return a.name.localeCompare(b.name);
  });

  const topPlayer = sortedPlayers[0];
  const hasJackpotWinner = gameState.players.some((player) => player.isJackpot);
  const hasWinner = gameState.players.some((player) => player.isWon);

  const formattedTotalScore = new Intl.NumberFormat('fr-FR').format(
    gameState.totalScore,
  );
  const formattedJackpotThreshold = new Intl.NumberFormat('fr-FR').format(
    gameState.jackpotThreshold,
  );
  const formattedMinThreshold = new Intl.NumberFormat('fr-FR').format(
    gameState.minThreshold,
  );

  const gameProgress =
    gameState.gameTimer > 0 ? (gameState.gameTimer / 240) * 100 : 0;

  const sessionProgress =
    gameState.sessionDuration && gameState.sessionDuration > 0
      ? (gameState.sessionDuration / 1200) * 100
      : 0;

  const getStatusMessage = () => {
    if (!isConnected) {
      return 'Connexion au serveur en cours...';
    }

    switch (gameState.gameStatus) {
      case 'waiting':
        return 'En attente - Présentez votre badge pour commencer';
      case 'welcome':
        return 'Bienvenue ! Le jeu va commencer...';
      case 'playing':
        return 'Jeu en cours';
      case 'complete':
        return gameState.resultDescription || 'Jeu terminé';
      default:
        return 'État inconnu';
    }
  };

  const getPlayerRank = (player: Player): number => {
    return sortedPlayers.findIndex((p) => p.name === player.name) + 1;
  };

  const isPlayerWinner = (player: Player): boolean => {
    return player.isWon || player.isJackpot;
  };

  const getPlayerStatus = (player: Player): string[] => {
    const status = [];
    if (player.isJackpot) status.push('JACKPOT');
    else if (player.isWon) status.push('GAGNANT');
    else status.push('PERDU');
    return status;
  };

  const gameSummary = {
    isActive: isGameActive,
    isWaiting: isWaitingForGame,
    isComplete: isGameComplete,
    hasWinner,
    hasJackpot: hasJackpotWinner,
    totalPlayers: gameState.players.length,
    topScore: topPlayer?.points || 0,
    averageScore:
      gameState.players.length > 0
        ? Math.round(
            gameState.players.reduce((sum, p) => sum + p.points, 0) /
              gameState.players.length,
          )
        : 0,
  };

  return {
    gameState,
    isConnected,

    gameTimer,
    sessionTimer,
    isGameTimerActive,
    isSessionTimerActive,
    gameProgress,
    sessionProgress,

    players: gameState.players,
    sortedPlayers,
    topPlayer,
    hasJackpotWinner,
    hasWinner,

    formattedTotalScore,
    formattedJackpotThreshold,
    formattedMinThreshold,

    statusMessage: getStatusMessage(),
    gameSummary,

    getPlayerRank,
    isPlayerWinner,
    getPlayerStatus,
    sendMessage,
  };
}
