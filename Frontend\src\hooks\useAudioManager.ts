import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

export const useAudioManager = () => {
  const location = useLocation();

  // Refs to persist audio elements across renders
  const backgroundMusicRef = useRef<HTMLAudioElement | null>(null);
  const victorySoundRef = useRef<HTMLAudioElement | null>(null);
  const lossSoundRef = useRef<HTMLAudioElement | null>(null);
  const isJackpotTransitionRef = useRef<boolean>(false);

  // Initialize audio elements
  useEffect(() => {
    // Create audio elements
    const backgroundMusic = new Audio('/audios/music.mp3');
    const victorySound = new Audio('/audios/victory.mp3');
    const lossSound = new Audio('/audios/loss.mp3');

    // Configure background music for looping
    backgroundMusic.loop = true;
    backgroundMusic.volume = 0.7;

    // Configure sound effects
    victorySound.volume = 0.8;
    lossSound.volume = 0.8;

    // Store in refs
    backgroundMusicRef.current = backgroundMusic;
    victorySoundRef.current = victorySound;
    lossSoundRef.current = lossSound;

    // Cleanup function
    return () => {
      backgroundMusic.pause();
      victorySound.pause();
      lossSound.pause();
      backgroundMusic.src = '';
      victorySound.src = '';
      lossSound.src = '';
    };
  }, []);

  // Simple audio control functions
  const stopAllAudio = () => {
    if (backgroundMusicRef.current && !backgroundMusicRef.current.paused) {
      backgroundMusicRef.current.pause();
      backgroundMusicRef.current.currentTime = 0;
    }
    if (victorySoundRef.current && !victorySoundRef.current.paused) {
      victorySoundRef.current.pause();
      victorySoundRef.current.currentTime = 0;
    }
    if (lossSoundRef.current && !lossSoundRef.current.paused) {
      lossSoundRef.current.pause();
      lossSoundRef.current.currentTime = 0;
    }
    isJackpotTransitionRef.current = false;
  };

  const playBackgroundMusic = () => {
    if (!backgroundMusicRef.current) return;

    // Stop other audio but keep background music if already playing
    if (victorySoundRef.current && !victorySoundRef.current.paused) {
      victorySoundRef.current.pause();
      victorySoundRef.current.currentTime = 0;
    }
    if (lossSoundRef.current && !lossSoundRef.current.paused) {
      lossSoundRef.current.pause();
      lossSoundRef.current.currentTime = 0;
    }

    if (backgroundMusicRef.current.paused) {
      backgroundMusicRef.current.currentTime = 0;
      backgroundMusicRef.current
        .play()
        .catch((error) =>
          console.warn('Failed to play background music:', error),
        );
      console.log('🎵 Background music started');
    }
  };

  const playVictorySound = () => {
    if (!victorySoundRef.current) return;

    // Stop other audio but don't interrupt victory sound if already playing
    if (backgroundMusicRef.current && !backgroundMusicRef.current.paused) {
      backgroundMusicRef.current.pause();
      backgroundMusicRef.current.currentTime = 0;
    }
    if (lossSoundRef.current && !lossSoundRef.current.paused) {
      lossSoundRef.current.pause();
      lossSoundRef.current.currentTime = 0;
    }

    if (victorySoundRef.current.paused) {
      victorySoundRef.current.currentTime = 0;
      victorySoundRef.current
        .play()
        .catch((error) => console.warn('Failed to play victory sound:', error));
      console.log('🎉 Victory sound started');
    }
  };

  const playLossSound = () => {
    if (!lossSoundRef.current) return;

    // Stop other audio
    if (backgroundMusicRef.current && !backgroundMusicRef.current.paused) {
      backgroundMusicRef.current.pause();
      backgroundMusicRef.current.currentTime = 0;
    }
    if (victorySoundRef.current && !victorySoundRef.current.paused) {
      victorySoundRef.current.pause();
      victorySoundRef.current.currentTime = 0;
    }

    if (lossSoundRef.current.paused) {
      lossSoundRef.current.currentTime = 0;
      lossSoundRef.current
        .play()
        .catch((error) => console.warn('Failed to play loss sound:', error));
      console.log('💔 Loss sound started');
    }
  };

  // Simple route-based audio management
  useEffect(() => {
    if (
      !backgroundMusicRef.current ||
      !victorySoundRef.current ||
      !lossSoundRef.current
    ) {
      return;
    }

    const currentPath = location.pathname;
    console.log(`🎵 Audio manager: route changed to ${currentPath}`);

    switch (currentPath) {
      case '/welcome':
      case '/instructions':
        playBackgroundMusic();
        break;

      case '/won':
        // Don't restart victory sound if it's already playing (jackpot transition)
        if (!isJackpotTransitionRef.current) {
          playVictorySound();
        } else {
          console.log(
            '🎰→🏆 Jackpot to win transition: keeping victory sound playing',
          );
          isJackpotTransitionRef.current = false; // Reset flag
        }
        break;

      case '/jackpot':
        playVictorySound();
        break;

      case '/lost':
        playLossSound();
        break;

      case '/':
        stopAllAudio();
        console.log('🔇 All audio stopped (home page)');
        break;
    }
  }, [location.pathname]);

  // Handle jackpot → win transition (keep victory sound playing)
  const handleJackpotToWinTransition = () => {
    isJackpotTransitionRef.current = true;
    console.log(
      '🎰→🏆 Jackpot to win transition: setting flag to keep victory sound playing',
    );
  };

  return {
    playBackgroundMusic,
    playVictorySound,
    playLossSound,
    stopAllAudio,
    handleJackpotToWinTransition,
  };
};
