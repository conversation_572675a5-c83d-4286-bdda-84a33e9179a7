# Hardware Configuration Guide

The application supports flexible hardware configuration with **always-available mock NFC** functionality.

## Key Principle

**Mock NFC (press 'b') is ALWAYS available** regardless of configuration. The configuration only controls whether to **also** try real hardware.

## Hardware Components

- **NFC Reader**: Badge scanning - Mock always available + optional real hardware
- **Controllino**: Sensor bus and arcade machine communication (SIM or PROD)
- **LED Control**: LED indicators (SIM or PROD)

## NFC Reader Configuration

- **NONE**: Only mock NFC (press 'b' to enter badge IDs)
- **PCSC**: Mock NFC + PCSC-compatible hardware readers
- **RS232**: Mock NFC + Serial-based hardware readers

## Configuration Methods

### 1. Configuration File (src/config/global.json)

```json
{
  "hardware": {
    "nfcReaderType": "NONE",
    "modes": {
      "controllino": "SIM",
      "ledControl": "SIM"
    }
  }
}
```

### 2. Environment Variables (Override config file)

```bash
export NFC_READER_TYPE=PCSC
export CONTROLLINO_MODE=SIM
export LED_MODE=SIM
npm run start
```

## Quick Start Examples

### Default Mode (Mock NFC Only)
```bash
npm run start
```
- NFC: Mock only (press 'b' to simulate badges)
- Controllino: SIM
- LEDs: SIM

### With PCSC Hardware Reader
```bash
export NFC_READER_TYPE=PCSC
npm run start
```
- NFC: Mock + PCSC hardware
- Controllino: SIM
- LEDs: SIM

### With RS232 Hardware Reader
```bash
export NFC_READER_TYPE=RS232
npm run start
```
- NFC: Mock + RS232 hardware
- Controllino: SIM
- LEDs: SIM

### Full Production Mode
```bash
export NFC_READER_TYPE=PCSC
export CONTROLLINO_MODE=PROD
export LED_MODE=PROD
npm run start
```
- NFC: Mock + PCSC hardware
- Controllino: PROD
- LEDs: PROD

## Important Notes

- **Press 'b' simulation is ALWAYS available** regardless of configuration
- **NONE**: Only mock NFC, no hardware attempted
- **PCSC/RS232**: Mock NFC + hardware reader (both work simultaneously)

## Use Cases

### Development & Testing
- Use `controllino: "SIM"` to avoid needing real Controllino hardware
- Use `nfcReader: "SIM"` for keyboard-based badge simulation
- Use `ledControl: "SIM"` to see LED commands in logs instead of real LEDs

### Production with Partial Hardware
- Use `nfcReader: "PROD"` with `controllino: "SIM"` when Controllino is not connected
- Gradually enable hardware components as they become available

### Debugging
- Switch individual components to SIM mode to isolate hardware issues
- Use mock services to test game logic without hardware dependencies

## Current Default Configuration

Based on your `global.json`:
- NFC Reader: PROD (uses real RS232/PCSC reader)
- Controllino: SIM (uses mock sensor service)
- LED Control: SIM (uses mock LED service)  
- Display: PROD (uses WebSocket display)

This allows you to test with real NFC badges while keeping Controllino simulated.
