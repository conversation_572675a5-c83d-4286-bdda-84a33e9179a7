// Updated PlayerData interface according to GameManagerResource specification
export interface PlayerData {
  id: number;
  badgeId: string;
  badgeActivated: boolean;
  displayName: string;
  firstName: string;
  lastName: string;
  points: number;
  isJackpot: boolean;
  avatarUrl?: string;
  team: {
    id: number;
    name: string;
  };
}

// Legacy interface for backward compatibility (to be removed after refactoring)
export interface PlayerDTO {
    id: number;
    badgeId: string;
    displayName: string;
    avatarUrl?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    points?: number;
}
  