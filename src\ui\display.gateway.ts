import { WebSocketGateway, WebSocketServer, OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit } from '@nestjs/websockets';
import { Server, WebSocket } from 'ws';
import { Logger, Injectable } from '@nestjs/common';
import { DisplayMessage } from './dto/display-message.dto';

@Injectable()
@WebSocketGateway(8000, { path: '/ws', cors: { origin: '*' } })
export class DisplayGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {
  private readonly log = new Logger(DisplayGateway.name);
  @WebSocketServer() server: Server;

  afterInit(server: Server) {
    this.log.log('🚀 WebSocket Gateway initialized on port 8000 with path /ws');
  }

  handleConnection(client: WebSocket) {
    this.log.log(`💻  Client connected (${this.server.clients.size} total)`);
  }

  handleDisconnect(client: WebSocket) {
    this.log.log(`💻  Client disconnected (${this.server.clients.size} total)`);
  }

  /** Sends a typed message to the connected client (single client mode) */
  send(msg: DisplayMessage) {
    const json = JSON.stringify(msg);
    // 🔭 Log every payload for easier debugging
    this.log.debug(`📡  WS payload → ${json}`);

    // Send to the first connected client (single client mode)
    const clients = Array.from(this.server.clients);
    if (clients.length > 0) {
      const client = clients[0] as WebSocket;
      if (client.readyState === client.OPEN) {
        client.send(json);
      }
    } else {
      this.log.warn('No WebSocket clients connected');
    }
  }

  /** Broadcasts a typed message to every connected client (legacy method) */
  broadcast(msg: DisplayMessage) {
    const json = JSON.stringify(msg);
    // 🔭 Log every payload for easier debugging
    this.log.debug(`📡  WS payload → ${json}`);
    this.server.clients.forEach((c) => c.readyState === c.OPEN && c.send(json));
  }
}