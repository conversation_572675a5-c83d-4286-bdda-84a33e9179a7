import { Injectable } from '@nestjs/common';
import { DisplayService } from '../hardware/interfaces/display.interface';
import { DisplayGateway } from './display.gateway';

@Injectable()
export class WebsocketDisplayService implements DisplayService {
  private lastScore = 0;
  constructor(private gateway: DisplayGateway) {}

  /** Send game start message with full game configuration */
  sendGameStart(gameConfig: any): void {
    try {
      this.gateway.send({
        action: 'start',
        payload: gameConfig,
      });
      console.log('[WEBSOCKET] Game start sent:', gameConfig);
    } catch (err) {
      console.warn('[WEBSOCKET] Failed to send game start:', err);
    }
  }

  /** Send game completion with player scores and states */
  sendGameComplete(players: any[], gameScore: number = 0): void {
    try {
      this.gateway.send({
        action: 'game_complete',
        payload: {
          players: players,
          gameScore: gameScore,
        },
      });
      console.log('[WEBSOCKET] Game complete sent:', { players, gameScore });
    } catch (err) {
      console.warn('[WEBSOCKET] Failed to send game complete:', err);
    }
  }

  /** Send cell ready message */
  sendCellReady(): void {
    try {
      this.gateway.send({
        action: 'cell_ready',
        payload: {
          message: 'Cell is ready for new team',
        },
      });
      console.log('[WEBSOCKET] Cell ready sent');
    } catch (err) {
      console.warn('[WEBSOCKET] Failed to send cell ready:', err);
    }
  }

  /** Send transition to win screen message */
  sendTransitionToWin(): void {
    try {
      this.gateway.send({
        action: 'transition_to_win',
        payload: {
          message: 'Transition from jackpot to win screen',
        },
      });
      console.log('[WEBSOCKET] Transition to win sent');
    } catch (err) {
      console.warn('[WEBSOCKET] Failed to send transition to win:', err);
    }
  }

  /** Ignored – front‑end shows its own splash once it receives `{action:'start'}` */
  showSplash(title: string, subtitle?: string): void {
    line('🎮  ' + title);
    if (subtitle) console.log(subtitle);
  }

  /** Emits ±delta each time strategy updates the score */
  updateScore(score: number): void {
    const diff = score - this.lastScore;
    if (diff !== 0) this.gateway.broadcast({ action: 'bonus', points: diff });
    this.lastScore = score;
    console.log('[SCORE] ' + score);
  }

  /** No per‑second timer update – countdown is done client‑side */
  updateTimer(sec: number): void {
    console.log('[TIMER] ' + sec + 's left');
  }

  /** Called once by GameEngine when the round ends */
  showResult(serialised: string): void {
    let data: any;
    try {
      data = JSON.parse(serialised);
    } catch {
      // payload is not valid JSON
      data = { message: serialised };
    }
    try {
      // Safe broadcast: if no clients or error, it's caught
      this.gateway.broadcast({ action: 'end', points: data.score });
    } catch (err) {
      console.warn(`WebSocket broadcast failed.`);
    }

    // auto‑reset 10 s later
    this.lastScore = 0;
    setTimeout(() => {
      try {
        this.gateway.broadcast({ action: 'reset' });
      } catch {}
    }, 10_000);

    line('🏁 RESULT');
    console.log(data.score ?? data.message);
  }
}

function line(title: string) {
  console.log('='.repeat(40));
  console.log(title);
  console.log('='.repeat(40));
}
