import { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useWebSocket } from '../context/WebSocketContext';
import type { GameRoute } from '../types';

export function useAutoNavigation() {
  const navigate = useNavigate();
  const location = useLocation();
  const { gameState } = useWebSocket();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const clearNavTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };

  const navigateWithDelay = (route: GameRoute, delay: number = 0) => {
    clearNavTimeout();

    if (delay === 0) {
      navigate(route);
    } else {
      timeoutRef.current = setTimeout(() => {
        navigate(route);
        timeoutRef.current = null;
      }, delay);
    }
  };

  useEffect(() => {
    const currentPath = location.pathname as GameRoute;
    const isResultScreen = ['/lost', '/won', '/jackpot'].includes(currentPath);

    if (isResultScreen && gameState.gameStatus === 'complete') {
      console.log(
        `📍 On result screen: ${currentPath}, waiting for cell_ready message (frontend is slave)`,
      );
    }

    return clearNavTimeout;
  }, [location.pathname, gameState.gameStatus]);

  useEffect(() => {
    const currentPath = location.pathname as GameRoute;

    if (currentPath === '/welcome' && gameState.gameStatus === 'welcome') {
      console.log(
        '📍 On welcome screen, auto-navigating to instructions in 4 seconds',
      );
      navigateWithDelay('/instructions', 4000);
    }

    return clearNavTimeout;
  }, [location.pathname, gameState.gameStatus]);

  useEffect(() => {
    return clearNavTimeout;
  }, []);

  return {
    navigateWithDelay,
    clearNavTimeout,
    currentRoute: location.pathname as GameRoute,
  };
}
