#!/usr/bin/env node

/**
 * WebSocket Test Client for Arcade Game System
 * 
 * This script connects to the arcade game WebSocket server and displays
 * real-time game events including game start, completion, and cell status.
 * 
 * Usage:
 *   node websocket-test-client.js [server-ip]
 * 
 * Example:
 *   node websocket-test-client.js *************
 *   node websocket-test-client.js localhost
 */

const WebSocket = require('ws');

// Configuration
const DEFAULT_SERVER = 'localhost';
const WS_PORT = 8000;
const WS_PATH = '/ws';

// Get server IP from command line argument or use default
const serverIp = process.argv[2] || DEFAULT_SERVER;
const wsUrl = `ws://${serverIp}:${WS_PORT}${WS_PATH}`;

console.log('🎮 Arcade Game WebSocket Test Client');
console.log('=====================================');
console.log(`📡 Connecting to: ${wsUrl}`);
console.log('⏰ Timestamp format: [HH:MM:SS]');
console.log('');

// Create WebSocket connection
const ws = new WebSocket(wsUrl);

// Helper function to get current timestamp
function getTimestamp() {
  const now = new Date();
  return now.toTimeString().split(' ')[0];
}

// Helper function to format console output
function logMessage(type, message, data = null) {
  const timestamp = getTimestamp();
  console.log(`[${timestamp}] ${type}: ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
  console.log('');
}

// WebSocket event handlers
ws.on('open', function open() {
  logMessage('🔗 CONNECTION', 'Connected to arcade game server');
});

ws.on('message', function message(data) {
  try {
    const parsed = JSON.parse(data.toString());
    const { action, payload } = parsed;

    switch (action) {
      case 'start':
        logMessage('🚀 GAME START', 'New game session started', {
          'Game Name': payload.gameName,
          'Team Name': payload.teamName,
          'Instructions': payload.instructions,
          'Game Timer': `${payload.gameTimer} seconds`,
          'Session Duration': `${payload.sessionDuration} seconds`,
          'Jackpot Threshold': `${payload.jackpotThreshold} points`,
          'Min Threshold': `${payload.minThreshold} points`
        });
        break;

      case 'game_complete':
        logMessage('🏁 GAME COMPLETE', 'Game session finished');
        
        console.log('👥 PLAYER RESULTS:');
        payload.players.forEach((player, index) => {
          const status = [];
          if (player.isWon) status.push('✅ WON');
          else status.push('❌ LOST');
          if (player.isJackpot) status.push('🎰 JACKPOT');
          
          console.log(`   ${index + 1}. ${player.name}: ${player.points} points ${status.join(' ')}`);
        });
        console.log('');
        break;

      case 'cell_ready':
        logMessage('🟢 CELL READY', 'Cell is available for new team', {
          'Status': 'Ready for badge scan',
          'Message': payload.message
        });
        break;

      case 'bonus':
        logMessage('💰 SCORE UPDATE', `Score bonus: +${payload.points} points`);
        break;

      case 'end':
        logMessage('🎯 GAME END', `Final score: ${payload.points} points`);
        break;

      case 'reset':
        logMessage('🔄 RESET', 'Game system reset');
        break;

      default:
        logMessage('❓ UNKNOWN', `Unknown action: ${action}`, parsed);
        break;
    }
  } catch (error) {
    logMessage('❌ ERROR', `Failed to parse message: ${error.message}`, {
      'Raw Data': data.toString()
    });
  }
});

ws.on('error', function error(err) {
  logMessage('❌ CONNECTION ERROR', err.message);
});

ws.on('close', function close(code, reason) {
  logMessage('🔌 DISCONNECTED', `Connection closed (Code: ${code})`, {
    'Reason': reason.toString() || 'No reason provided',
    'Code': code
  });
  
  console.log('💡 Tips:');
  console.log('   - Check if the arcade game server is running');
  console.log('   - Verify the server IP address and port');
  console.log('   - Make sure WebSocket port 8000 is accessible');
  console.log('');
  
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', function() {
  console.log('\n👋 Closing WebSocket connection...');
  ws.close();
  process.exit(0);
});

// Keep the script running
console.log('🎯 Waiting for game events...');
console.log('   Press Ctrl+C to exit');
console.log('');
