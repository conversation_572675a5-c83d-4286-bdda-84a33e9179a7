import React from 'react';
import Header from "./Header";
import Footer from "./Footer";
import { useAutoNavigation } from '../hooks/useAutoNavigation';

interface ContainerProps {
  children: React.ReactNode,
  background: string
}

export default function Container({ children, background }: ContainerProps) {
  useAutoNavigation();

  return (
    <div className="relative w-full h-full overflow-hidden bg-no-repeat bg-center bg-cover" style={{ backgroundImage: `url(/img/${background})`, width: '1920px', height: '1080px' }}>

      <div className="w-full h-full overflow-hidden" style={{ backgroundImage: `url(/img/text-background.png)` }}>

        <Header />

        {children}

        <Footer />

      </div>

    </div>
  )
}
