import { useWebSocket } from '../context/WebSocketContext';

function formatTime(seconds: number | null | undefined): string {
  if (seconds === null || seconds === undefined) return 'ERROR';
  if (seconds <= 0) return '00:00';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`;
}

export function useTimers() {
  const { gameState } = useWebSocket();

  const gameTimer = formatTime(gameState.gameTimer);
  const sessionTimer = formatTime(gameState.sessionDuration);

  const isGameTimerActive =
    gameState.gameTimer > 0 && gameState.gameStatus !== 'waiting';
  const isSessionTimerActive =
    gameState.sessionDuration !== null &&
    gameState.sessionDuration !== undefined &&
    gameState.sessionDuration > 0 &&
    gameState.gameStatus !== 'waiting';

  return {
    gameTimer,
    sessionTimer,
    isGameTimerActive,
    isSessionTimerActive,
    gameTimerSeconds: gameState.gameTimer,
    sessionTimerSeconds: gameState.sessionDuration,
  };
}
