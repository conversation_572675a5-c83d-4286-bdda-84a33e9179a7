import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import globalConfig from './config/global';
import { HardwareModule } from './hardware/hardware.module';
import { ApiModule } from './api/api.module';
import { UiModule } from './ui/ui.module';
import { GameCoreModule } from './games/game-core/game-core.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [globalConfig],
      ignoreEnvFile: true, // Ignore .env file completely
    }),
    // Import HardwareModule based on config file mode
    HardwareModule.register(globalConfig().mode === 'PROD' ? 'PROD' : 'SIM'),
    ApiModule,
    UiModule,
    GameCoreModule,
  ],
})
export class AppModule {}