import React, { createContext, useContext, type ReactNode } from 'react';
import { useAudioManager } from '../hooks/useAudioManager';

interface AudioContextType {
  playBackgroundMusic: () => void;
  playVictorySound: () => void;
  playLossSound: () => void;
  stopAllAudio: () => void;
  handleJackpotToWinTransition: () => void;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

interface AudioProviderProps {
  children: ReactNode;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {
  const audioManager = useAudioManager();

  return (
    <AudioContext.Provider value={audioManager}>
      {children}
    </AudioContext.Provider>
  );
};

export const useAudio = (): AudioContextType => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};
