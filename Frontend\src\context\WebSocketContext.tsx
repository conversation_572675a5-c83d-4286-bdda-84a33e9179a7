import React, { createContext, useContext, useReducer, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import type {
  GameState,
  WebSocketContextType,
  WebSocketMessage,
  StartPayload,
  GameCompletePayload,
  BonusPayload,
  EndPayload
} from '../types';
import { useAudio } from './AudioContext';

const initialGameState: GameState = {
  isConnected: false,
  connectionError: null,
  reconnectAttempts: 0,
  lastConnectedAt: null,
  gameName: 'Nom de l\'épreuve',
  teamName: 'Nom de l\'équipe',
  instructions: '',
  gameTimer: 0,
  sessionDuration: null,
  totalScore: 0,
  gameScore: 0,
  players: [],
  currentRoute: '/',
  gameStatus: 'waiting',
  jackpotThreshold: 5000,
  minThreshold: 1000,
  gameResult: null,
  resultDescription: ''
};

type GameAction =
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_CONNECTION_ERROR'; payload: string | null }
  | { type: 'SET_RECONNECT_ATTEMPTS'; payload: number }
  | { type: 'START_GAME'; payload: StartPayload }
  | { type: 'UPDATE_GAME_TIMER'; payload: number }
  | { type: 'UPDATE_SESSION_TIMER'; payload: number | null }
  | { type: 'ADD_BONUS'; payload: number }
  | { type: 'GAME_COMPLETE'; payload: GameCompletePayload }
  | { type: 'SET_ROUTE'; payload: string }
  | { type: 'SET_GAME_STATUS'; payload: GameState['gameStatus'] }
  | { type: 'RESET_GAME' }
  | { type: 'UPDATE_TOTAL_SCORE'; payload: number };

function gameStateReducer(state: GameState, action: GameAction): GameState {
  switch (action.type) {
    case 'SET_CONNECTED':
      return {
        ...state,
        isConnected: action.payload,
        connectionError: action.payload ? null : state.connectionError,
        lastConnectedAt: action.payload ? new Date() : state.lastConnectedAt,
        reconnectAttempts: action.payload ? 0 : state.reconnectAttempts
      };

    case 'SET_CONNECTION_ERROR':
      return { ...state, connectionError: action.payload };

    case 'SET_RECONNECT_ATTEMPTS':
      return { ...state, reconnectAttempts: action.payload };

    case 'START_GAME':
      return {
        ...state,
        gameName: action.payload.gameName,
        teamName: action.payload.teamName,
        instructions: action.payload.instructions,
        gameTimer: action.payload.gameTimer,
        sessionDuration: action.payload.sessionDuration,
        totalScore: action.payload.teamScore,
        jackpotThreshold: action.payload.jackpotThreshold,
        minThreshold: action.payload.minThreshold,
        gameStatus: 'welcome',
        gameResult: null,
        resultDescription: ''
      };

    case 'UPDATE_GAME_TIMER':
      return { ...state, gameTimer: Math.max(0, action.payload) };

    case 'UPDATE_SESSION_TIMER':
      return { ...state, sessionDuration: action.payload === null ? null : Math.max(0, action.payload) };

    case 'ADD_BONUS':
      return { ...state, totalScore: state.totalScore + action.payload };

    case 'UPDATE_TOTAL_SCORE':
      return { ...state, totalScore: action.payload };

    case 'GAME_COMPLETE':
      const players = action.payload.players;
      const gameScore = action.payload.gameScore || 0;
      const hasJackpot = players.some(p => p.isJackpot);
      const hasWinner = players.some(p => p.isWon);

      let gameResult: 'won' | 'lost' | 'jackpot' = 'lost';
      let resultDescription = 'Insuffisant...';

      if (hasJackpot) {
        gameResult = 'jackpot';
        resultDescription = 'Jackpot !';
      } else if (hasWinner) {
        gameResult = 'won';
        resultDescription = 'Félicitations !';
      }

      return {
        ...state,
        players: players,
        gameScore: gameScore,
        gameStatus: 'complete',
        gameResult,
        resultDescription
      };

    case 'SET_ROUTE':
      return { ...state, currentRoute: action.payload };

    case 'SET_GAME_STATUS':
      return { ...state, gameStatus: action.payload };

    case 'RESET_GAME':
      return {
        ...initialGameState,
        isConnected: state.isConnected,
        connectionError: state.connectionError,
        reconnectAttempts: state.reconnectAttempts,
        lastConnectedAt: state.lastConnectedAt
      };

    default:
      return state;
  }
}

const WebSocketContext = createContext<WebSocketContextType | null>(null);

export function WebSocketProvider({ children }: { children: React.ReactNode }) {
  const [gameState, dispatch] = useReducer(gameStateReducer, initialGameState);
  const wsRef = useRef<WebSocket | null>(null);
  const navigate = useNavigate();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const gameTimerRef = useRef<NodeJS.Timeout | null>(null);
  const { handleJackpotToWinTransition } = useAudio();
  const sessionTimerRef = useRef<NodeJS.Timeout | null>(null);
  const autoNavigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const WS_URL = 'ws://localhost:8000/ws';
  const BASE_RECONNECT_DELAY = 1000;
  const MAX_RECONNECT_DELAY = 30000;
  const MAX_RECONNECT_ATTEMPTS = 10;
  const startGameTimer = (seconds: number) => {
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
    }

    let currentTime = seconds;
    dispatch({ type: 'UPDATE_GAME_TIMER', payload: currentTime });

    gameTimerRef.current = setInterval(() => {
      currentTime -= 1;
      dispatch({ type: 'UPDATE_GAME_TIMER', payload: currentTime });

      if (currentTime <= 0) {
        clearInterval(gameTimerRef.current!);
        gameTimerRef.current = null;
      }
    }, 1000);
  };

  const startSessionTimer = (seconds: number | null) => {
    if (sessionTimerRef.current) {
      clearInterval(sessionTimerRef.current);
    }

    // Don't start timer if no valid session duration from API
    if (seconds === null || seconds === undefined) {
      dispatch({ type: 'UPDATE_SESSION_TIMER', payload: null });
      return;
    }

    let currentTime = seconds;
    dispatch({ type: 'UPDATE_SESSION_TIMER', payload: currentTime });

    sessionTimerRef.current = setInterval(() => {
      currentTime -= 1;
      dispatch({ type: 'UPDATE_SESSION_TIMER', payload: currentTime });

      if (currentTime <= 0) {
        clearInterval(sessionTimerRef.current!);
        sessionTimerRef.current = null;
      }
    }, 1000);
  };

  const stopTimers = () => {
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
      gameTimerRef.current = null;
    }
    if (sessionTimerRef.current) {
      clearInterval(sessionTimerRef.current);
      sessionTimerRef.current = null;
    }
  };

  const navigateWithTimeout = (route: string, delay: number = 0) => {
    if (autoNavigationTimeoutRef.current) {
      clearTimeout(autoNavigationTimeoutRef.current);
    }

    autoNavigationTimeoutRef.current = setTimeout(() => {
      dispatch({ type: 'SET_ROUTE', payload: route });
      navigate(route);
    }, delay);
  };

  const handleMessage = (event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      const { action, payload } = message;

      console.log('WebSocket message received:', { action, payload });

      switch (action) {
        case 'start':
          dispatch({ type: 'START_GAME', payload: payload as StartPayload });
          startGameTimer(payload.gameTimer);
          startSessionTimer(payload.sessionDuration);

          console.log('🚀 Start message received: navigating immediately to /welcome');
          dispatch({ type: 'SET_ROUTE', payload: '/welcome' });
          navigate('/welcome');
          console.log('⏰ Setting 4-second timeout to navigate to /instructions');
          navigateWithTimeout('/instructions', 4000);
          break;

        case 'game_complete':
          // Only stop the game timer, keep session timer running
          if (gameTimerRef.current) {
            clearInterval(gameTimerRef.current);
            gameTimerRef.current = null;
          }
          const gameCompletePayload = payload as GameCompletePayload;
          dispatch({ type: 'GAME_COMPLETE', payload: gameCompletePayload });

          const players = gameCompletePayload.players;
          const hasJackpot = players.some(p => p.isJackpot);
          const hasWinner = players.some(p => p.isWon);

          let resultRoute = '/lost';
          if (hasJackpot) {
            resultRoute = '/jackpot';
          } else if (hasWinner) {
            resultRoute = '/won';
          }

          console.log(`🎯 Game complete: navigating to ${resultRoute} (hasJackpot: ${hasJackpot}, hasWinner: ${hasWinner})`);
          navigateWithTimeout(resultRoute, 0);

          if (hasJackpot) {
            console.log('🎰 Jackpot detected - waiting for transition_to_win message after 10s');
          } else {
            console.log('⏳ Waiting for cell_ready message to return to home after 10s');
          }
          break;

        case 'transition_to_win':
          console.log('🏆 Transition to win: navigating from jackpot to win screen');
          handleJackpotToWinTransition(); // Keep victory sound playing
          navigateWithTimeout('/won', 0);
          console.log('⏳ Waiting for cell_ready message to return to home after 10s');
          break;

        case 'cell_ready':
          // Stop session timer and reset game when returning to home
          stopTimers();
          dispatch({ type: 'RESET_GAME' });
          const currentPath = window.location.pathname;
          console.log(`🏠 Cell ready: current path is ${currentPath}, navigating to home`);
          navigateWithTimeout('/', 0);
          break;

        case 'bonus':
          const bonusPoints = (payload as BonusPayload).points;
          dispatch({ type: 'ADD_BONUS', payload: bonusPoints });
          console.log(`💰 Bonus received: +${bonusPoints} points`);
          break;

        case 'end':
          const finalScore = (payload as EndPayload).points;
          dispatch({ type: 'UPDATE_TOTAL_SCORE', payload: finalScore });
          console.log(`🎯 Final score: ${finalScore} points`);
          break;

        case 'reset':
          dispatch({ type: 'RESET_GAME' });
          stopTimers();
          navigateWithTimeout('/', 0);
          break;

        default:
          console.warn('Unknown WebSocket action:', action);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  const connect = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    dispatch({ type: 'SET_CONNECTION_ERROR', payload: null });

    try {
      console.log(`Connecting to WebSocket: ${WS_URL}`);
      wsRef.current = new WebSocket(WS_URL);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        dispatch({ type: 'SET_CONNECTED', payload: true });
        dispatch({ type: 'SET_RECONNECT_ATTEMPTS', payload: 0 });

        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
      };

      wsRef.current.onmessage = handleMessage;

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected', { code: event.code, reason: event.reason });
        dispatch({ type: 'SET_CONNECTED', payload: false });

        if (event.code !== 1000 && gameState.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
          const nextAttempt = gameState.reconnectAttempts + 1;
          dispatch({ type: 'SET_RECONNECT_ATTEMPTS', payload: nextAttempt });

          const delay = Math.min(
            BASE_RECONNECT_DELAY * Math.pow(2, nextAttempt - 1),
            MAX_RECONNECT_DELAY
          );

          console.log(`Attempting to reconnect in ${delay}ms (attempt ${nextAttempt}/${MAX_RECONNECT_ATTEMPTS})`);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else if (gameState.reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
          console.error('Max reconnection attempts reached');
          dispatch({ type: 'SET_CONNECTION_ERROR', payload: 'Connexion impossible après plusieurs tentatives' });
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        dispatch({ type: 'SET_CONNECTED', payload: false });
        dispatch({ type: 'SET_CONNECTION_ERROR', payload: 'Erreur de connexion WebSocket' });
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      dispatch({ type: 'SET_CONNECTED', payload: false });
      dispatch({ type: 'SET_CONNECTION_ERROR', payload: `Erreur de connexion: ${error instanceof Error ? error.message : 'Erreur inconnue'}` });
    }
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    stopTimers();
    dispatch({ type: 'SET_CONNECTED', payload: false });
  };

  const sendMessage = (message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected. Cannot send message:', message);
    }
  };

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, []);

  useEffect(() => {
    return () => {
      if (autoNavigationTimeoutRef.current) {
        clearTimeout(autoNavigationTimeoutRef.current);
      }
      stopTimers();
    };
  }, []);

  const contextValue: WebSocketContextType = {
    gameState,
    isConnected: gameState.isConnected,
    connect,
    disconnect,
    sendMessage
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocket(): WebSocketContextType {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
}
