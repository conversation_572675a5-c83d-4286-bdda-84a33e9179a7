import { Module } from '@nestjs/common';
import { GameEngineService } from './game-engine.service';
import { GameTimerService } from './game-timer.service';
import { ApiModule } from 'src/api/api.module';
import { UiModule } from 'src/ui/ui.module';

@Module({
  imports: [
    // HardwareModule is global, so providers are automatically available
    ApiModule,
    UiModule,
  ],
  providers: [
    GameEngineService,
    GameTimerService
  ],
  exports: [],
})
export class GameCoreModule { }