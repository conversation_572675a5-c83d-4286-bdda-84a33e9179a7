import { registerAs } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

export default registerAs('global', () => {
  const file = path.join(__dirname, 'global.json');
  const cfg: any = JSON.parse(fs.readFileSync(file, 'utf8'));

  return {
    port: cfg.port,
    gameId: cfg.gameId,
    stationId: cfg.stationId,
    mode: cfg.mode,

    api: {
      baseUrl: cfg.api.baseUrl,
      playerEndpoint: cfg.api.playerEndpoint,
      scoreEndpoint: cfg.api.scoreEndpoint,
    },

    hardware: {
      ...cfg.hardware,
      // Use only config file values - no fallbacks
      nfcReaderType: cfg.hardware.nfcReaderType,
      modes: {
        controllino: cfg.hardware.modes?.controllino,
        ledControl: cfg.hardware.modes?.ledControl,
      },
    },
    game: cfg.game,
    adminBadges: cfg.adminBadges,
  };
});
