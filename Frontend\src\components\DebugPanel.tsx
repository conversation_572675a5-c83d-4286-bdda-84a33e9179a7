import { useState } from 'react';
import { useGameState } from '../hooks/useGameState';
import { useAudio } from '../context/AudioContext';
import ConnectionStatus from './ConnectionStatus';

interface DebugPanelProps {
  isVisible?: boolean;
}

export default function DebugPanel({ isVisible = false }: DebugPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { gameState, sendMessage, gameTimer, sessionTimer } = useGameState();
  const { audioState } = useAudio();

  if (!isVisible) return null;

  const simulateStart = () => {
    sendMessage({
      action: 'start',
      payload: {
        gameName: 'Mini Golf Challenge (Debug)',
        teamName: 'Team Debug',
        instructions: 'Visez la cible et obtenez un maximum de points',
        gameTimer: 240,
        sessionDuration: 1200,
        teamScore: 1500,
        jackpotThreshold: 5000,
        minThreshold: 1000
      }
    });
  };

  const simulateBonus = () => {
    sendMessage({
      action: 'bonus',
      payload: { points: 500 }
    });
  };

  const simulateGameComplete = () => {
    sendMessage({
      action: 'game_complete',
      payload: {
        players: [
          { name: 'Player 1', points: 1500, isWon: true, isJackpot: false },
          { name: 'Player 2', points: 800, isWon: false, isJackpot: false }
        ],
        gameScore: 2300
      }
    });
  };

  const simulateJackpot = () => {
    sendMessage({
      action: 'game_complete',
      payload: {
        players: [
          { name: 'Player 1', points: 6000, isWon: true, isJackpot: true }
        ],
        gameScore: 6000
      }
    });
  };

  const simulateReset = () => {
    sendMessage({
      action: 'reset',
      payload: {}
    });
  };

  return (
    <div className="fixed top-4 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg max-w-sm">
      <div
        className="p-3 bg-gray-100 cursor-pointer flex justify-between items-center"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="font-semibold text-sm">Debug Panel</span>
        <span className="text-xs">{isExpanded ? '▼' : '▶'}</span>
      </div>

      {isExpanded && (
        <div className="p-4 space-y-4">
          <ConnectionStatus showDetails={true} />

          <div>
            <h4 className="font-semibold text-sm mb-2">Game State</h4>
            <div className="text-xs space-y-1">
              <div>Status: {gameState.gameStatus}</div>
              <div>Game Timer: {gameTimer}</div>
              <div>Session Timer: {sessionTimer}</div>
              <div>Total Score: {gameState.totalScore}</div>
              <div>Players: {gameState.players.length}</div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-sm mb-2">Audio State</h4>
            <div className="text-xs space-y-1">
              <div>Currently Playing: {audioState.currentlyPlaying || 'None'}</div>
              <div>Background Music: {audioState.backgroundMusic ? '✅' : '❌'}</div>
              <div>Victory Sound: {audioState.victorySound ? '✅' : '❌'}</div>
              <div>Loss Sound: {audioState.lossSound ? '✅' : '❌'}</div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-sm mb-2">Simulate Messages</h4>
            <div className="space-y-2">
              <button
                onClick={simulateStart}
                className="w-full px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
              >
                Start Game
              </button>
              <button
                onClick={simulateBonus}
                className="w-full px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600"
              >
                Add Bonus (+500)
              </button>
              <button
                onClick={simulateGameComplete}
                className="w-full px-3 py-1 bg-yellow-500 text-white text-xs rounded hover:bg-yellow-600"
              >
                Game Complete (Win)
              </button>
              <button
                onClick={simulateJackpot}
                className="w-full px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600"
              >
                Jackpot Win
              </button>
              <button
                onClick={simulateReset}
                className="w-full px-3 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
