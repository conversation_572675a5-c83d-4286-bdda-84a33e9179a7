import Container from "../components/Container";
import ContentText from "../components/ContentText";
import { useGameState } from "../hooks/useGameState";

export default function Jackpot() {
  const { gameState } = useGameState();

  // Find ALL jackpot winners
  const jackpotWinners = gameState.players.filter(player => player.isJackpot);

  // Create display text for single or multiple winners
  const getJackpotText = () => {
    if (jackpotWinners.length === 0) {
      return "Joueur : vous avez gagné le jackpot";
    } else if (jackpotWinners.length === 1) {
      return `${jackpotWinners[0].name.toUpperCase()} : vous avez gagné le jackpot`;
    } else {
      // Multiple jackpot winners
      const names = jackpotWinners.map(p => p.name.toUpperCase()).join(" & ");
      return `${names} : vous avez gagné le jackpot`;
    }
  };

  return (
    <Container background='rideaux-en-or.png'>

      <img
        src="/img/pluie-de-tickets.gif"
        alt="Pluie de tickets"
        className="fixed top-[0px] left-[0px] w-full h-full z-50 pointer-events-none object-cover"
      />

      <ContentText ticket='ticket-en-or.gif' personnage='jackpot.gif' title="jackpot" status="JACKPOT">
        <div className="font-alfa text-[28px] font-[500] uppercase text-center">{getJackpotText()}</div>
      </ContentText>
    </Container>
  )
}
