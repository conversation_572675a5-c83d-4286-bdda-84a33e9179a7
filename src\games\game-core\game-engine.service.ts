import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  NFC_READER,
  SENSOR_BUS,
  DISPLAY,
  LED_CONTROL,
} from '../../hardware/tokens';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { SensorBusService } from '../../hardware/interfaces/sensor-bus.interface';
import { DisplayService } from '../../hardware/interfaces/display.interface';
import { LedControlService } from '../../hardware/interfaces/led-control.interface';
import { ConfigService } from '@nestjs/config';
import { Subscription } from 'rxjs';
import { ApiService } from '../../api/api.service';
import { GameTimerService } from './game-timer.service';
import { DisplayGateway } from 'src/ui/display.gateway';
import { ControllinoSensorService } from '../../hardware/sensors/controllino-sensor.service';
import {
  TeamScoreRequest,
  PlayerScoreData,
} from '../../common/dto/game‑manager.dto';

// Interface for controllino score response - now individual player scores
interface ControllinoScoreResponse {
  [playerOrder: string]: number; // Player order (1-4) -> score
}

@Injectable()
export class GameEngineService {
  private readonly log = new Logger(GameEngineService.name);
  private arcadeMachineOutput: string;
  private scoreSubscription?: Subscription;
  private gameInProgress = false;
  private currentTeam: any = null;
  private cellOccupied = false;
  private exitBufferActive = false;
  private hasJackpotPlayers = false; // Track if current game has jackpot players

  constructor(
    @Inject(NFC_READER) private nfc: NfcReaderService,
    @Inject(SENSOR_BUS) private sensors: SensorBusService,
    @Inject(DISPLAY) private display: DisplayService,
    @Inject(LED_CONTROL) private led: LedControlService,
    private cfg: ConfigService,
    private api: ApiService,
    private gameTimer: GameTimerService,
    private ui: DisplayGateway,
  ) {
    // Get arcade machine output mapping from config
    this.arcadeMachineOutput = this.cfg.get<string>(
      'hardware.controllino.outputs.arcadeMachine',
      '04',
    );

    this.log.log(`Arcade machine output mapping: ${this.arcadeMachineOutput}`);

    // Delay NFC listener setup to ensure PCSC reader is fully initialized
    setTimeout(() => {
      this.initializeEventListeners();
    }, 1000);

    this.setAvailableState();
  }

  private initializeEventListeners() {
    this.log.log('🔗 Setting up NFC listeners...');

    this.nfc.onTag().subscribe({
      next: (tag: string) => {
        this.handleBadgeScan(tag);
      },
      error: (error) => {
        this.log.error(`NFC error: ${error}`);
      },
    });

    this.log.log('✅ NFC listeners ready');
  }

  private setAvailableState() {
    this.gameInProgress = false;
    this.display.showSplash('Scan your team badge');
    this.setGreenIndicator();
    console.log('🟢 Cell is available - ready for badge scan');
  }

  private async handleBadgeScan(tag: string) {
    // Guard: Check if cell is occupied or in exit buffer
    if (this.gameInProgress || this.cellOccupied || this.exitBufferActive) {
      console.log('🚫 Cell is currently in use - please wait');
      return; // Ignore badge scan
    }

    const gameId = this.cfg.get<number>('global.gameId') ?? 1;

    const adminBadges = this.cfg.get<string[]>('global.adminBadges') ?? [];
    const isAdmin = adminBadges.includes(tag);

    let teamData: any;
    let players: any[] = [];

    if (isAdmin) {
      this.log.warn(`Admin badge '${tag}' detected – bypassing API`);
      teamData = { id: 0, name: 'Admin Team' };
      players = [
        { id: 1, displayName: 'Admin Player 1' },
        { id: 2, displayName: 'Admin Player 2' },
        { id: 3, displayName: 'Admin Player 3' },
        { id: 4, displayName: 'Admin Player 4' },
      ];
    } else {
      this.log.log(
        `[AUTH_API] Calling authorizeTeam with badge: ${tag}, gameId: ${gameId}`,
      );
      const auth = await this.api.authorizeTeam(tag, gameId).catch((error) => {
        this.log.error(`[AUTH_API] Authorization API call failed: ${error}`);
        return null;
      });

      this.log.log(`[AUTH_API] Authorization response:`);
      this.log.log(`[AUTH_API] ${JSON.stringify(auth, null, 2)}`);

      if (!auth || auth.code !== 200 || !auth.team || !auth.players) {
        this.log.error(
          `[AUTH_API] Authorization failed - Code: ${
            auth?.code
          }, Team: ${!!auth?.team}, Players: ${!!auth?.players}`,
        );
        console.log('❌ Authorization failed - please try again');
        return; // Return to main loop
      }
      teamData = auth.team;
      players = auth.players;

      this.log.log(
        `[AUTH_API] Authorization successful - Team: ${teamData.name}, Players: ${players.length}`,
      );
    }

    // Clean display of team info
    console.log(`✅ Team authorized: ${teamData.name}`);
    console.log(`👥 Players: ${players.map((p) => p.displayName).join(', ')}`);

    // Log team score details for debugging
    this.log.log(`[TEAM_SCORE_DEBUG] Team data received from API:`);
    this.log.log(`[TEAM_SCORE_DEBUG] - Team ID: ${teamData.id}`);
    this.log.log(`[TEAM_SCORE_DEBUG] - Team Name: ${teamData.name}`);
    this.log.log(`[TEAM_SCORE_DEBUG] - Team Points: ${teamData.points}`);
    this.log.log(
      `[TEAM_SCORE_DEBUG] - Team Object: ${JSON.stringify(teamData, null, 2)}`,
    );

    // Set cell state and hardware
    this.currentTeam = teamData;
    this.cellOccupied = true;

    // Proper authorization sequence: LED → Light → Door
    this.setRedIndicator(); // Turn red LED on, green LED off
    this.powerOnCell(); // Turn on cell light
    this.openDoor(); // Open door for team entry

    // Send WebSocket game start message
    await this.sendGameStartWebSocket(gameId, teamData, players);

    // Start the arcade game
    await this.startArcadeGame(teamData, players, isAdmin, gameId);
  }

  private async startArcadeGame(
    teamData: any,
    players: any[],
    isAdmin: boolean,
    gameId: number,
  ) {
    this.gameInProgress = true; // Mark cell as occupied

    try {
      console.log('🎮 Starting game...');

      // WebSocket start message is sent in handleBadgeScan after authorization

      // Send team setup and start arcade machine
      // Red LED already set when team was authorized (occupied state)
      this.sendTeamSetupCommand(players);
      this.sendArcadeMachineCommand(true);
      this.display.showSplash('Game in progress...');

      // Wait for controllino to send score response
      const scores = await this.waitForControllinoScores(players);

      // Turn off arcade machine
      this.sendArcadeMachineCommand(false);

      console.log('🏁 Game Complete!');

      // Display clean score results
      Object.entries(scores).forEach(([playerOrder, points]) => {
        const player = players[parseInt(playerOrder) - 1];
        const isJackpot =
          points >= (this.cfg.get<number>('game.jackpotThreshold') || 1000);
        if (isJackpot) {
          console.log(`🎰 JACKPOT! ${player.displayName}: ${points} points`);
        } else {
          console.log(`⭐ ${player.displayName}: ${points} points`);
        }
      });

      // Submit team scores to API (if not admin)
      if (!isAdmin) {
        await this.submitTeamScores(gameId, players, scores);
        console.log('✅ Scores submitted successfully');
      }

      // Send game completion to WebSocket client
      await this.sendGameCompleteWebSocket(gameId, players, scores);

      // Start exit buffer period (keep cell occupied for user exit)
      await this.startExitBuffer();

      // Show success state for 3 seconds, then return to available
      await new Promise((resolve) => setTimeout(resolve, 3000));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Game error: ${errorMessage}`);
      this.sendArcadeMachineCommand(false); // Ensure machine is turned off
      this.setRedIndicator(); // Keep red (error state)
      this.display.showResult('Game Error - Please try again');

      // Wait 3 seconds then return to available state
      await new Promise((resolve) => setTimeout(resolve, 3000));
    } finally {
      // Game state will be reset by exit buffer - don't call setAvailableState here
      this.gameInProgress = false;
    }
  }

  private sendTeamSetupCommand(players: any[]): void {
    // Create binary team setup command: A01 + 4 UIDs (4 bytes each)
    const playerCount = players.length;
    this.log.log(
      `Sending binary team setup command for ${playerCount} players`,
    );

    // Create 35-byte buffer: A01 + 32 bytes for UIDs (8 bytes per player * 4 players)
    const buf = Buffer.alloc(35);

    // Command header: A01
    buf[0] = 0x41; // 'A'
    buf[1] = 0x30; // '0'
    buf[2] = 0x31; // '1'

    // Fill player UIDs (convert badge IDs to 8-byte ASCII)
    for (let i = 0; i < 4; i++) {
      const startIndex = 3 + i * 8;

      if (i < players.length && players[i].badgeId) {
        const rawBadgeId = players[i].badgeId.toString();
        this.log.log(
          `Player ${i + 1}: ${players[i].displayName} (${rawBadgeId})`,
        );

        // Convert full 8-character badge ID to ASCII bytes (e.g., "ZZZZWWWW" -> [0x5A, 0x5A, 0x5A, 0x5A, 0x57, 0x57, 0x57, 0x57])
        const badgeId = rawBadgeId.padEnd(8, '\0').substring(0, 8);

        // Convert each character to its ASCII byte value
        for (let j = 0; j < 8; j++) {
          buf[startIndex + j] = badgeId.charCodeAt(j);
        }

        const hexBytes = Array.from(buf.subarray(startIndex, startIndex + 8))
          .map((b) => b.toString(16).padStart(2, '0').toUpperCase())
          .join(' ');
        this.log.log(`Player ${i + 1} ASCII: ${rawBadgeId} -> ${hexBytes}`);
      } else {
        // Pad with zeros for missing players (8 bytes)
        buf.fill(0x00, startIndex, startIndex + 8);
        this.log.log(`Player ${i + 1}: [EMPTY - padded with 0x00]`);
      }
    }

    this.log.log(`Binary command: ${buf.toString('hex').toUpperCase()}`);

    // Send binary command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendBinaryCommand) {
      controllinoSensor.sendBinaryCommand(buf);
    } else {
      this.log.warn(
        'Cannot send binary team setup command - sensor service does not support sendBinaryCommand',
      );
    }
  }

  private sendArcadeMachineCommand(turnOn: boolean): void {
    const stateOn =
      this.cfg.get<string>('hardware.controllino.states.on') || '1';
    const stateOff =
      this.cfg.get<string>('hardware.controllino.states.off') || '0';
    const command = `O${this.arcadeMachineOutput}+${
      turnOn ? stateOn : stateOff
    }`;
    this.log.log(`Sending arcade machine command: ${command}`);

    // Get the controllino sensor service to send the command
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      controllinoSensor.sendCommand(command);
    } else {
      this.log.warn(
        'Cannot send command - sensor service does not support sendCommand',
      );
    }
  }

  private async waitForControllinoScores(
    players: any[],
  ): Promise<ControllinoScoreResponse> {
    const gameId = this.cfg.get<number>('global.gameId') ?? 1;
    const mode = this.cfg.get<string>('global.mode', 'PROD');

    // Get appropriate timeout based on mode
    const timeoutMs =
      mode === 'SIM'
        ? await this.gameTimer.getSimBackupTimeout(gameId)
        : await this.gameTimer.getProdTimeout(gameId);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(async () => {
        if (this.scoreSubscription) {
          this.scoreSubscription.unsubscribe();
        }

        // On timeout, create zero scores for all players with isWon = false
        this.log.warn(
          `[TIMEOUT] Game timed out after ${
            timeoutMs / 1000
          }s - creating zero scores`,
        );
        const timeoutScores: ControllinoScoreResponse = {};
        for (let i = 1; i <= players.length; i++) {
          timeoutScores[i.toString()] = 0;
        }

        resolve(timeoutScores); // Resolve with zero scores instead of rejecting
      }, timeoutMs);

      this.log.log(
        `Waiting for individual score events from ${players.length} players...`,
      );

      const receivedScores: ControllinoScoreResponse = {};
      let expectedPlayerCount = players.length;
      let simulationTimeoutId: NodeJS.Timeout | null = null;

      // Listen to arduino.js compatible EventInput events
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        const eventHandler = (numEvent: string, input: number) => {
          this.log.log(
            `Received score event: Player ${numEvent}, Score: ${input}`,
          );

          // numEvent should be '1', '2', '3', or '4' (player order)
          if (['1', '2', '3', '4'].includes(numEvent)) {
            receivedScores[numEvent] = input;

            // Check if we have all expected scores
            const receivedCount = Object.keys(receivedScores).length;
            this.log.log(
              `Received ${receivedCount}/${expectedPlayerCount} player scores`,
            );

            if (receivedCount >= expectedPlayerCount) {
              // All scores received - clear all timeouts
              controllinoSensor.emitter.off('EventInput', eventHandler);
              clearTimeout(timeout);
              if (simulationTimeoutId) {
                clearTimeout(simulationTimeoutId);
                simulationTimeoutId = null;
              }

              this.log.log(
                `All scores received: ${JSON.stringify(receivedScores)}`,
              );
              resolve(receivedScores);
            }
          }
        };

        controllinoSensor.emitter.on('EventInput', eventHandler);
      } else {
        this.log.warn(
          'EventEmitter not available on controllino sensor service',
        );
        reject(new Error('EventEmitter not available'));
      }

      // For testing/simulation: auto-generate scores using configured timeout
      if (mode === 'SIM') {
        this.setupSimulationScoreGeneration(
          resolve,
          reject,
          timeout,
          players,
          gameId,
        ).then((timeoutId) => {
          simulationTimeoutId = timeoutId;
        });
      }
    });
  }

  private async setupSimulationScoreGeneration(
    resolve: (value: ControllinoScoreResponse) => void,
    _reject: (reason?: any) => void,
    timeout: any,
    players: any[],
    gameId: number,
  ): Promise<NodeJS.Timeout> {
    // Get configured timeout for simulation mode
    const simTimeoutMs = await this.gameTimer.getSimTimeout(gameId);

    // In simulation mode, auto-generate scores using configured timeout
    return setTimeout(async () => {
      const config = await this.gameTimer.getGameTimerConfig(gameId);
      this.log.log(
        `Simulation mode: Auto-generating test scores after ${config.gameTimer}s (${config.source})`,
      );

      if (this.scoreSubscription) {
        this.scoreSubscription.unsubscribe();
      }
      clearTimeout(timeout);

      // Get jackpot threshold from config
      const jackpotThreshold = this.cfg.get<number>(
        'global.game.thresholds.jackpot',
      );
      if (jackpotThreshold === undefined) {
        throw new Error(
          'Missing required config: global.game.thresholds.jackpot',
        );
      }

      // Generate individual player score events (1-4)
      const testScores: ControllinoScoreResponse = {};

      // Simulate individual EventInput events for each player
      const controllinoSensor = this.sensors as any;
      if (controllinoSensor.emitter) {
        players.forEach((player, index) => {
          const playerOrder = (index + 1).toString(); // 1, 2, 3, 4

          // 20% chance of jackpot score
          const isJackpotRoll = Math.random() < 0.2;

          let score: number;
          if (isJackpotRoll) {
            // Jackpot: threshold + random 0-500
            score = jackpotThreshold + Math.floor(Math.random() * 500);
            this.log.log(
              `🎰 Jackpot generated for Player ${playerOrder} (${player.displayName}): ${score} points!`,
            );
          } else {
            // Normal score: 100 to (threshold - 1)
            const maxNormal = jackpotThreshold - 1;
            score = Math.floor(Math.random() * (maxNormal - 100 + 1)) + 100;
          }

          testScores[playerOrder] = score;

          // Emit individual EventInput event with slight delay
          setTimeout(() => {
            controllinoSensor.emitter.emit('EventInput', playerOrder, score);
            this.log.log(
              `Simulated EventInput: Player ${playerOrder}, Score: ${score}`,
            );
          }, index * 100); // 100ms delay between each player
        });

        this.log.log(
          `Generated individual score events for ${players.length} players`,
        );
      } else {
        // Fallback: resolve with scores directly
        this.log.warn('EventEmitter not available, using direct resolution');
        resolve(testScores);
      }
    }, simTimeoutMs); // Use configured simulation timeout
  }

  private async submitTeamScores(
    gameId: number,
    players: any[],
    scores: ControllinoScoreResponse,
  ): Promise<void> {
    try {
      // Get game configuration (thresholds, etc.)
      const gameConfig = await this.gameTimer.getGameTimerConfig(gameId);

      this.log.log(
        `[SCORE_PROCESSING] Processing scores for ${players.length} players`,
      );
      this.log.log(
        `[SCORE_PROCESSING] Thresholds - Jackpot: ${gameConfig.jackpotThreshold}, MinThreshold: ${gameConfig.minThreshold} (${gameConfig.source})`,
      );

      // Calculate total team score
      const totalTeamScore = Object.values(scores).reduce(
        (sum, score) => sum + score,
        0,
      );
      this.log.log(
        `[SCORE_PROCESSING] Total team score: ${totalTeamScore} points`,
      );

      // Determine if team won (total >= minThreshold)
      const teamWon = totalTeamScore >= gameConfig.minThreshold;
      this.log.log(
        `[SCORE_PROCESSING] Team ${
          teamWon ? 'WON' : 'LOST'
        } (${totalTeamScore} >= ${gameConfig.minThreshold}: ${teamWon})`,
      );

      // Map player order scores to actual players
      const playerScores: PlayerScoreData[] = [];

      for (let i = 0; i < players.length; i++) {
        const player = players[i];
        const playerOrder = (i + 1).toString(); // 1, 2, 3, 4
        let playerPoints = scores[playerOrder];

        // If team lost (total < threshold), set all player scores to 0
        if (!teamWon && playerPoints !== undefined) {
          playerPoints = 0;
          this.log.log(
            `[SCORE_PROCESSING] Team lost - setting Player ${i + 1} (${
              player.displayName
            }) score to 0`,
          );
        }

        if (playerPoints !== undefined) {
          // Check individual player jackpot (individual score >= jackpotThreshold)
          const isJackpot = playerPoints >= gameConfig.jackpotThreshold;

          // Team win status applies to all players (team total >= minThreshold)
          const isWon = teamWon;

          playerScores.push({
            playerId: player.id,
            playerPoints: playerPoints,
            isJackpot: isJackpot,
            isWon: isWon,
          });

          this.log.log(
            `[SCORE_PROCESSING] Player ${i + 1} (${
              player.displayName
            }): ${playerPoints} points${isJackpot ? ' 🎰 JACKPOT!' : ''}${
              isWon ? ' ✅ WON' : ' ❌ LOST'
            }`,
          );
        } else {
          this.log.warn(
            `No score found for Player ${i + 1} (${player.displayName})`,
          );
        }
      }

      if (playerScores.length === 0) {
        this.log.warn('No valid scores to submit');
        return;
      }

      const scoreRequest: TeamScoreRequest = {
        gameId,
        players: playerScores,
      };

      // Log final submission summary
      const jackpotCount = playerScores.filter((p) => p.isJackpot).length;
      const wonCount = playerScores.filter((p) => p.isWon).length;
      this.log.log(
        `[SCORE_PROCESSING] Submitting scores for ${playerScores.length} players (${jackpotCount} jackpots, ${wonCount} winners)`,
      );

      const response = await this.api.createTeamScore(scoreRequest);

      if (response.code === 200) {
        this.log.log(
          `[SCORE_PROCESSING] Team scores submitted successfully - Team: ${
            response.team?.name || 'unknown'
          }'}`,
        );
      } else {
        this.log.error(
          `[SCORE_PROCESSING] Failed to submit scores: ${response.message}`,
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.log.error(`Error submitting team scores: ${errorMessage}`);
    }
  }

  // Arduino.js compatible LED and door control methods
  private setGreenIndicator(): void {
    // Green LED ON = Available/Ready (like arduino.js turnOnGreenIndicator)
    this.log.log('Setting GREEN indicator (available)');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const greenLed =
        this.cfg.get<string>('hardware.controllino.outputs.greenLed') || '02';
      const redLed =
        this.cfg.get<string>('hardware.controllino.outputs.redLed') || '01';
      const stateOn =
        this.cfg.get<string>('hardware.controllino.states.on') || '1';
      const stateOff =
        this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${greenLed}+${stateOn}`); // Green LED ON
      controllinoSensor.sendCommand(`O${redLed}+${stateOff}`); // Red LED OFF
    }
  }

  private setRedIndicator(): void {
    // Red LED ON = Occupied/In Use (like arduino.js turnOffGreenIndicator)
    this.log.log('Setting RED indicator (occupied)');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const greenLed =
        this.cfg.get<string>('hardware.controllino.outputs.greenLed') || '02';
      const redLed =
        this.cfg.get<string>('hardware.controllino.outputs.redLed') || '01';
      const stateOn =
        this.cfg.get<string>('hardware.controllino.states.on') || '1';
      const stateOff =
        this.cfg.get<string>('hardware.controllino.states.off') || '0';

      controllinoSensor.sendCommand(`O${redLed}+${stateOn}`); // Red LED ON
      controllinoSensor.sendCommand(`O${greenLed}+${stateOff}`); // Green LED OFF
    }
  }

  private powerOnCell(): void {
    // Turn on cell light (like arduino.js powerOnCell)
    this.log.log('Turning ON cell light');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const cellLight = this.cfg.get<string>(
        'global.hardware.controllino.outputs.cellLight',
        '99',
      );
      const stateOn = this.cfg.get<string>(
        'global.hardware.controllino.states.on',
        '1',
      );

      controllinoSensor.sendCommand(`O${cellLight}+${stateOn}`); // Cell light ON
    }
  }

  private powerOffCell(): void {
    // Turn off cell light (like arduino.js powerOffCell)
    this.log.log('Turning OFF cell light');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const cellLight = this.cfg.get<string>(
        'global.hardware.controllino.outputs.cellLight',
        '99',
      );
      const stateOff = this.cfg.get<string>(
        'global.hardware.controllino.states.off',
        '0',
      );

      controllinoSensor.sendCommand(`O${cellLight}+${stateOff}`); // Cell light OFF
    }
  }

  private openDoor(): void {
    // Open door temporarily (like arduino.js openDoor)
    // Note: Door logic is reversed - OUT_OFF opens, OUT_ON closes
    this.log.log('Opening door temporarily');
    const controllinoSensor = this.sensors as ControllinoSensorService;
    if (controllinoSensor.sendCommand) {
      const door = this.cfg.get<string>(
        'global.hardware.controllino.outputs.door',
        '05',
      );
      const stateOn = this.cfg.get<string>(
        'global.hardware.controllino.states.on',
        '1',
      );
      const stateOff = this.cfg.get<string>(
        'global.hardware.controllino.states.off',
        '0',
      );

      controllinoSensor.sendCommand(`O${door}+${stateOff}`); // Door OPEN (reversed logic)

      // Close door after 10 seconds (like arduino.js DURATION_DOOR)
      setTimeout(() => {
        controllinoSensor.sendCommand(`O${door}+${stateOn}`); // Door CLOSE (reversed logic)
        this.log.log('Door closed after timeout');
      }, 10000);
    }
  }

  private async startExitBuffer(): Promise<void> {
    const exitBufferSeconds = this.cfg.get<number>(
      'global.game.displayTimings.exitBuffer',
    );
    if (exitBufferSeconds === undefined) {
      throw new Error(
        'Missing required config: global.game.displayTimings.exitBuffer',
      );
    }
    this.exitBufferActive = true;

    this.log.log(
      `[EXIT_BUFFER] Starting ${exitBufferSeconds}s exit buffer - cell remains occupied for user exit`,
    );

    return new Promise((resolve) => {
      setTimeout(() => {
        if (this.hasJackpotPlayers) {
          // Jackpot scenario: show jackpot, then win
          const jackpotDisplaySeconds = this.cfg.get<number>(
            'global.game.displayTimings.jackpotDisplay',
          );
          const winDisplaySeconds = this.cfg.get<number>(
            'global.game.displayTimings.winDisplay',
          );

          if (jackpotDisplaySeconds === undefined) {
            throw new Error(
              'Missing required config: global.game.displayTimings.jackpotDisplay',
            );
          }
          if (winDisplaySeconds === undefined) {
            throw new Error(
              'Missing required config: global.game.displayTimings.winDisplay',
            );
          }

          this.log.log(
            `[EXIT_BUFFER] Exit buffer complete - jackpot detected, showing jackpot for ${jackpotDisplaySeconds}s`,
          );

          // After jackpot display time, send transition to win screen
          setTimeout(() => {
            this.log.log(
              `[JACKPOT_BUFFER] Jackpot display complete - transitioning to win screen for ${winDisplaySeconds}s`,
            );
            this.sendTransitionToWinWebSocket();

            // After win display time, send cell_ready
            setTimeout(() => {
              this.log.log(
                `[WIN_BUFFER] Win display complete - now resetting cell state`,
              );
              this.resetCellState();
              this.exitBufferActive = false;
              this.hasJackpotPlayers = false; // Reset jackpot flag
              resolve();
            }, winDisplaySeconds * 1000);
          }, jackpotDisplaySeconds * 1000);
        } else {
          // Normal scenario: show result then reset
          const resultDisplaySeconds = this.cfg.get<number>(
            'global.game.displayTimings.resultDisplay',
          );

          if (resultDisplaySeconds === undefined) {
            throw new Error(
              'Missing required config: global.game.displayTimings.resultDisplay',
            );
          }
          this.log.log(
            `[EXIT_BUFFER] Exit buffer complete - showing result for ${resultDisplaySeconds}s before cell ready`,
          );

          setTimeout(() => {
            this.log.log(
              `[RESULT_BUFFER] Result display complete - now resetting cell state`,
            );
            this.resetCellState();
            this.exitBufferActive = false;
            this.hasJackpotPlayers = false; // Reset jackpot flag
            resolve();
          }, resultDisplaySeconds * 1000);
        }
      }, exitBufferSeconds * 1000);
    });
  }

  private resetCellState(): void {
    this.log.log('[CELL_RESET] Resetting cell to available state');

    // Reset cell state
    this.cellOccupied = false;
    this.currentTeam = null;
    this.gameInProgress = false;

    // Turn off cell light and set green LED (available)
    this.powerOffCell();
    this.setGreenIndicator();

    // Display ready message (single call)
    console.log('========================================');
    console.log('🎮  Scan your team badge');
    console.log('========================================');
    console.log('🟢 Cell is available - ready for badge scan');

    // Send WebSocket cell ready message
    this.sendCellReadyWebSocket();
  }

  private async sendGameStartWebSocket(
    gameId: number,
    teamData: any,
    _players: any[],
  ): Promise<void> {
    try {
      // Get game configuration
      const gameConfig = await this.gameTimer.getGameTimerConfig(gameId);

      // Fetch team details to calculate remaining session time from API only
      let sessionDuration: number | null = null;
      try {
        this.log.log(
          `[SESSION_API] Fetching team details for team ID: ${teamData.id}`,
        );
        const teamDetails = await this.api.getTeamDetails(teamData.id);

        this.log.log(`[SESSION_API] Raw team details response:`);
        this.log.log(`[SESSION_API] ${JSON.stringify(teamDetails, null, 2)}`);

        const totalSessionTime = teamDetails.gamePlay?.duration;
        const elapsedTime = teamDetails.session?.duration;

        this.log.log(`[SESSION_API] Extracted values:`);
        this.log.log(
          `[SESSION_API] - gamePlay.duration (total): ${totalSessionTime}`,
        );
        this.log.log(
          `[SESSION_API] - session.duration (elapsed): ${elapsedTime}`,
        );
        this.log.log(
          `[SESSION_API] - gamePlay object: ${JSON.stringify(
            teamDetails.gamePlay,
            null,
            2,
          )}`,
        );
        this.log.log(
          `[SESSION_API] - session object: ${JSON.stringify(
            teamDetails.session,
            null,
            2,
          )}`,
        );

        if (
          totalSessionTime !== null &&
          totalSessionTime !== undefined &&
          elapsedTime !== null &&
          elapsedTime !== undefined
        ) {
          // Calculate remaining time: Total - Elapsed
          sessionDuration = Math.max(0, totalSessionTime - elapsedTime);
          this.log.log(
            `[SESSION_CALC] Calculation: ${totalSessionTime} - ${elapsedTime} = ${sessionDuration}`,
          );
          this.log.log(
            `[SESSION_CALC] Remaining time: ${sessionDuration}s (${Math.floor(
              sessionDuration / 60,
            )}:${(sessionDuration % 60).toString().padStart(2, '0')})`,
          );
        } else {
          sessionDuration = null;
          this.log.warn(
            `[SESSION_CALC] Missing session data - Total: ${totalSessionTime}, Elapsed: ${elapsedTime}`,
          );
        }

        this.log.log(
          `[SESSION_API] Final sessionDuration to send to frontend: ${sessionDuration}`,
        );
      } catch (error) {
        this.log.error(`[SESSION_API] Failed to fetch team details: ${error}`);
        sessionDuration = null;
      }

      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendGameStart) {
        const payload = {
          gameName: gameConfig.gameName || 'Unknown Game',
          instructions: gameConfig.instructions || 'No instructions available',
          teamName: teamData.name,
          teamScore: teamData.points || 0, // Add team's current score
          jackpotThreshold: gameConfig.jackpotThreshold,
          minThreshold: gameConfig.minThreshold,
          gameTimer: gameConfig.gameTimer,
          sessionDuration: sessionDuration,
        };

        this.log.log(
          `[TEAM_SCORE_DEBUG] Sending to frontend - Team Score: ${payload.teamScore}`,
        );
        this.log.log(
          `[TEAM_SCORE_DEBUG] This is the team's accumulated score from all previous games`,
        );

        this.log.log(`[WEBSOCKET] Final payload being sent to frontend:`);
        this.log.log(`[WEBSOCKET] ${JSON.stringify(payload, null, 2)}`);

        websocketDisplay.sendGameStart(payload);
        this.log.log(
          `[WEBSOCKET] Game start sent - Team: ${teamData.name}, Session: ${payload.sessionDuration}s, Game: ${payload.gameTimer}s, Team Score: ${payload.teamScore}`,
        );
      }
    } catch (error) {
      this.log.error(`[WEBSOCKET] Failed to send game start: ${error}`);
    }
  }

  private async sendGameCompleteWebSocket(
    gameId: number,
    players: any[],
    scores: ControllinoScoreResponse,
  ): Promise<void> {
    try {
      // Get game configuration for thresholds
      const gameConfig = await this.gameTimer.getGameTimerConfig(gameId);

      // Calculate team total and win status
      const totalTeamScore = Object.values(scores).reduce(
        (sum, score) => sum + score,
        0,
      );
      const teamWon = totalTeamScore >= gameConfig.minThreshold;

      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendGameComplete) {
        const playersData = players.map((player, index) => {
          const playerOrder = (index + 1).toString();
          const playerPoints = scores[playerOrder] || 0;
          const isJackpot = playerPoints >= gameConfig.jackpotThreshold;

          return {
            name: player.displayName,
            points: playerPoints,
            isWon: teamWon,
            isJackpot: isJackpot,
          };
        });

        // Check if any players have jackpot for timing logic
        this.hasJackpotPlayers = playersData.some((player) => player.isJackpot);
        if (this.hasJackpotPlayers) {
          this.log.log(
            '[WEBSOCKET] 🎰 Jackpot detected - will use extended timing for cell ready',
          );
        }

        // Calculate game score from RAW scores (what players actually earned in the game)
        // This is for frontend display - always show real points earned, regardless of win/loss
        const gameScore = Object.values(scores).reduce(
          (sum, score) => sum + score,
          0,
        );

        this.log.log(
          `[GAME_SCORE_DEBUG] Raw individual scores: ${JSON.stringify(scores)}`,
        );
        this.log.log(
          `[GAME_SCORE_DEBUG] Player data for API (after win/loss logic): ${JSON.stringify(
            playersData.map((p) => ({
              name: p.name,
              points: p.points,
              isWon: p.isWon,
            })),
          )}`,
        );
        this.log.log(
          `[GAME_SCORE_DEBUG] Game score for frontend (raw total): ${gameScore}`,
        );

        websocketDisplay.sendGameComplete(playersData, gameScore);
        this.log.log(
          `[WEBSOCKET] Game complete sent to client - Game Score: ${gameScore}`,
        );
      }
    } catch (error) {
      this.log.error(`[WEBSOCKET] Failed to send game complete: ${error}`);
    }
  }

  private sendCellReadyWebSocket(): void {
    try {
      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendCellReady) {
        websocketDisplay.sendCellReady();
        this.log.log('[WEBSOCKET] Cell ready sent to client');
      }
    } catch (error) {
      this.log.error(`[WEBSOCKET] Failed to send cell ready: ${error}`);
    }
  }

  private sendTransitionToWinWebSocket(): void {
    try {
      const websocketDisplay = this.display as any;
      if (websocketDisplay.sendTransitionToWin) {
        websocketDisplay.sendTransitionToWin();
        this.log.log('[WEBSOCKET] Transition to win screen sent to client');
      }
    } catch (error) {
      this.log.error(`[WEBSOCKET] Failed to send transition to win: ${error}`);
    }
  }
}
